# KT-MK3 電表整合文件

**修改時間：** 2024年12月18日

## 概述

本次修改整合了 KT-MK3 電表到現有的電表管理系統中。KT-MK3 電表使用 Modbus RTU OverTCP 協定，支援 Input Register (32bit sw. float) 資料格式。

## 電表規格

- **電表名稱：** KT-MK3
- **通訊協定：** Modbus RTU OverTCP
- **資料格式：** Input Register (32bit sw. float)
- **功能碼：** 0x04 (讀取 Input Register)

## 測量項目與位址對應

| 項目 | 位址範圍 | 說明 | 縮放係數 |
|------|----------|------|----------|
| Vab | 7~8 | 線電壓 AB | 1 |
| Vbc | 9~10 | 線電壓 BC | 1 |
| Vca | 11~12 | 線電壓 CA | 1 |
| Ia | 13~14 | A相電流 | 1 |
| Ib | 15~16 | B相電流 | 1 |
| Ic | 17~18 | C相電流 | 1 |
| I | 67~68 | 總電流 | 1 |
| Kvar | 41~42 | 無功功率 | *0.001 |
| pf | 49~50 | 功率因數 | 1 |
| freq | 51~52 | 頻率 | 1 |
| kwh | 79~80 | 累積電能 | *0.001 |
| kw | 25~26 | 有功功率 | *0.001 |

## 修改檔案清單

### C++ 後端檔案

1. **新增檔案：**
   - `24dio/src/elecNodeKtMk3.hpp` - KT-MK3 電表標頭檔
   - `24dio/src/elecNodeKtMk3.cpp` - KT-MK3 電表實作檔

2. **修改檔案：**
   - `24dio/src/wdef.h` - 新增 `KT_MK3_ELEC_DEVICE = 51` 常數定義
   - `24dio/src/modbusNode.cpp` - 新增 KT-MK3 電表的 include 和處理邏輯
   - `24dio/src/modbusDev.cpp` - 在 `is_analog_device()` 函數中新增 KT-MK3 支援
   - `24dio/src/elecNode.cpp` - 新增 KT-MK3 的資料傳送格式處理
   - `24dio/CMakeLists.txt` - 在編譯清單中新增 `elecNodeKtMk3.cpp`

### PHP 前端檔案

3. **修改檔案：**
   - `web/com_floor-1.0.0/site/helpers/floor.php` - 新增 PHP 端的設備類型定義
   - `web/com_elec-1.0.0/site/helpers/elec.php` - 新增 PHP 端的電表設備定義
   - `web/com_top-1.0.0/site/helpers/top.php` - 在 `$rs485_devices` 陣列中新增 KT-MK3 電表項目
   - `web/com_top-1.0.0/site/helpers/toputility.php` - 新增 `$rs485_elec_device_kt_mk3` 常數定義

## 技術實作細節

### 32bit Float 資料轉換

KT-MK3 使用 32bit sw. float 格式，需要特殊的位元組重排：

```cpp
float elecNodeKtMk3::convertModbusFloatToFloat(uint8_t *p_data, int offset)
{
    uint8_t buf[4];
    buf[0] = p_data[offset + 1];     // 低位暫存器的低位元組
    buf[1] = p_data[offset];         // 低位暫存器的高位元組
    buf[2] = p_data[offset + 3];     // 高位暫存器的低位元組
    buf[3] = p_data[offset + 2];     // 高位暫存器的高位元組
    return *(float *)&buf[0];
}
```

### Modbus 指令建立

每個測量值使用獨立的 Modbus 讀取指令：
- 功能碼：0x04 (讀取 Input Register)
- 讀取長度：2 個暫存器 (32bit float)
- 包含 CRC 校驗碼

### 資料處理流程

1. 建立 12 個 Modbus 讀取指令（對應 12 個測量項目）
2. 循序讀取各項測量值
3. 將 32bit float 資料轉換為系統內部格式
4. 套用縮放係數（Kvar、kwh、kw 需乘以 0.001）
5. 更新電表資料並傳送至前端

## 設備類型編號

- **C++ 常數：** `KT_MK3_ELEC_DEVICE = 51`
- **PHP 常數：**
  - `$rs485_elec_devic_kt_mk3 = 51` (floor.php, elec.php)
  - `$rs485_elec_device_kt_mk3 = 51` (toputility.php)
- **前端選擇器：** `$rs485_devices[51] = 'KT-MK3電表'` (top.php)

## 注意事項

1. KT-MK3 電表的部分測量值（Kvar、kwh、kw）需要乘以 0.001 的縮放係數
2. 使用 Input Register 功能碼 (0x04) 而非 Holding Register (0x03)
3. 每個測量值佔用 2 個連續的暫存器（32bit float）
4. 資料轉換時需要注意位元組順序的正確性

## 測試建議

建議建立單元測試來驗證：
1. 32bit float 資料轉換的正確性
2. Modbus 指令的格式正確性
3. 縮放係數的套用正確性
4. 各測量值的資料對應正確性

## 相容性

此次修改與現有電表模組完全相容，不會影響其他電表類型的正常運作。
